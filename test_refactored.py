#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for refactored plugin
"""

import sys
import os

# Add the refactored plugin to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'refactored_plugin'))

def test_imports():
    """Test that all modules can be imported without errors"""
    print("Testing imports...")
    
    try:
        # Test constants
        from const import USER_ROLE, DIALOG_ACCEPTED, LINKED_CARDS_FIELD
        print("✓ Constants imported successfully")
        print(f"  USER_ROLE: {USER_ROLE}")
        print(f"  DIALOG_ACCEPTED: {DIALOG_ACCEPTED}")
        print(f"  LINKED_CARDS_FIELD: {LINKED_CARDS_FIELD}")
        
        # Test core module
        from core import CardLinker
        print("✓ Core module imported successfully")
        
        # Test dialogs module
        from dialogs import LinkDialog, SimpleAddCardDialog
        print("✓ Dialogs module imported successfully")
        
        # Test reviewer module
        from reviewer import add_linked_cards_to_review, setup_link_handler
        print("✓ Reviewer module imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_cardlinker_creation():
    """Test CardLinker instance creation"""
    print("\nTesting CardLinker creation...")
    
    try:
        from core import CardLinker
        card_linker = CardLinker()
        print("✓ CardLinker instance created successfully")
        print(f"  linked_cards_field: {card_linker.linked_cards_field}")
        return True
        
    except Exception as e:
        print(f"✗ CardLinker creation failed: {e}")
        return False

def test_module_structure():
    """Test the overall module structure"""
    print("\nTesting module structure...")
    
    try:
        # Test that we can import the main module
        import __init__ as main_module
        print("✓ Main module imported successfully")
        
        # Check if card_linker instance exists
        if hasattr(main_module, 'card_linker'):
            print("✓ card_linker instance found in main module")
        else:
            print("✗ card_linker instance not found in main module")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Module structure test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Testing Refactored Plugin")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_cardlinker_creation,
        test_module_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactoring appears to be successful.")
    else:
        print("❌ Some tests failed. Please check the issues above.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
