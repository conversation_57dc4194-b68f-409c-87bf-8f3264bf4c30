# refactor.py

import ast
import os
from collections import defaultdict

# 从配置文件导入我们的规则
from refactor_config import TARGET_FILES

class CodeClassifier(ast.NodeVisitor):
    def __init__(self, name_to_file_map):
        # 现在构造函数直接接收正确的映射
        self.name_to_file_map = name_to_file_map
        self.files = defaultdict(list)
        self.imports = []
        # 我们需要一个地方来存放未被分配的代码节点
        self.unassigned = []

    def visit_ClassDef(self, node):
        # 直接在映射中查找类名
        target_file = self.name_to_file_map.get(node.name)
        if target_file:
            self.files[target_file].append(node)
        else:
            print(f"Warning: Class '{node.name}' is not assigned to any file.")
            self.unassigned.append(node)

    def visit_FunctionDef(self, node):
        # 直接在映射中查找函数名
        target_file = self.name_to_file_map.get(node.name)
        if target_file:
            self.files[target_file].append(node)
        else:
            # 检查是否是那些我们期望保留在 __init__.py 或其他地方的特殊函数
            # 这里可以根据需要扩展
            if node.name not in ['setup_editor_buttons', 'on_reviewer_init', 'setup_link_handler']:
                 print(f"Warning: Function '{node.name}' is not assigned to any file.")
                 self.unassigned.append(node)

    def visit_Import(self, node):
        self.imports.append(node)
        # 不再有 generic_visit，因为我们只关心顶级节点

    def visit_ImportFrom(self, node):
        self.imports.append(node)
        # 不再有 generic_visit
        
    def visit_Assign(self, node):
        # 简单处理全局变量赋值，例如 card_linker = CardLinker()
        # 这个逻辑保持不变
        if isinstance(node.targets[0], ast.Name) and node.targets[0].id == 'card_linker':
             self.files['__init__.py'].append(node)
        else:
            # 其他顶级的赋值语句也认为是未分配的
            self.unassigned.append(node)

# --- 主逻辑 (main 函数保持不变) ---
def main():
    source_file = '__init__.py'
    output_dir = 'refactored_plugin'
    os.makedirs(output_dir, exist_ok=True)

    with open(source_file, 'r', encoding='utf-8') as f:
        source_code = f.read()

    # 1. 解析源代码为AST
    tree = ast.parse(source_code)

    # 2. 创建映射，将名称映射到文件名 (这部分是正确的)
    name_to_file_map = {name: fname for fname, names in TARGET_FILES.items() for name in names}

    # 3. 遍历和分类
    # 将正确的映射传递给分类器
    classifier = CodeClassifier(name_to_file_map)
    # 遍历所有顶级节点
    for node in tree.body:
        classifier.visit(node)

    # 4. 生成新文件
    for filename, nodes in classifier.files.items():
        if not nodes:
            continue
        
        # 构造新文件的AST
        # a. 添加所有原始导入 (这是一个简化策略)
        # b. 添加属于该文件的类和函数
        new_tree = ast.Module(body=classifier.imports + nodes, type_ignores=[])
        
        # 将AST转换回代码
        try:
            # Python 3.9+
            new_code = ast.unparse(new_tree)
        except AttributeError:
            # for older versions, use astor
            try:
                import astor
                new_code = astor.to_source(new_tree)
            except ImportError:
                print("Please install 'astor' for Python versions < 3.9: pip install astor")
                return

        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            # 添加UTF-8编码声明，这是一个好习惯
            f.write("# -*- coding: utf-8 -*-\n\n")
            f.write(new_code)
        print(f"Generated {filepath}")

    # 5. [重要] 后续处理
    print("\nRefactoring complete. Next steps:")
    print("1. Manually create/edit `__init__.py` to register hooks.")
    print("2. Manually handle constants in `const.py`.")
    print("3. Manually resolve circular dependencies (e.g., by using local imports).")
    print("4. Run a linter/formatter like `ruff --fix .` or `autoflake` on the new files to remove unused imports.")

if __name__ == '__main__':
    main()