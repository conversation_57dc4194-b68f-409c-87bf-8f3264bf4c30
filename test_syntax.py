#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Syntax test for refactored plugin - tests that all files have valid Python syntax
"""

import ast
import os

def test_file_syntax(filepath):
    """Test if a Python file has valid syntax"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the file to check syntax
        ast.parse(content)
        return True, None
        
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, f"Error reading file: {e}"

def main():
    """Test all Python files in the refactored plugin"""
    print("=" * 50)
    print("Testing Syntax of Refactored Plugin Files")
    print("=" * 50)
    
    plugin_dir = "refactored_plugin"
    python_files = [
        "__init__.py",
        "const.py", 
        "core.py",
        "dialogs.py",
        "reviewer.py"
    ]
    
    passed = 0
    total = len(python_files)
    
    for filename in python_files:
        filepath = os.path.join(plugin_dir, filename)
        
        if not os.path.exists(filepath):
            print(f"✗ {filename}: File not found")
            continue
            
        success, error = test_file_syntax(filepath)
        
        if success:
            print(f"✓ {filename}: Valid Python syntax")
            passed += 1
        else:
            print(f"✗ {filename}: {error}")
    
    print("\n" + "=" * 50)
    print(f"Syntax Test Results: {passed}/{total} files passed")
    
    if passed == total:
        print("🎉 All files have valid Python syntax!")
        print("The refactoring appears to be syntactically correct.")
    else:
        print("❌ Some files have syntax errors. Please fix them.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
