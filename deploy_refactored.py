#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deployment script for refactored AnkiNexus plugin
This script safely deploys the refactored code
"""

import os
import shutil
import datetime

def backup_original():
    """Backup the original __init__.py file"""
    original_file = "__init__.py"
    if os.path.exists(original_file):
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"__init__.py.backup_{timestamp}"
        shutil.copy2(original_file, backup_file)
        print(f"✓ Original file backed up as: {backup_file}")
        return backup_file
    else:
        print("✗ Original __init__.py not found")
        return None

def deploy_refactored_files():
    """Deploy the refactored files"""
    source_dir = "refactored_plugin"
    
    if not os.path.exists(source_dir):
        print(f"✗ Source directory '{source_dir}' not found")
        return False
    
    files_to_deploy = [
        "__init__.py",
        "const.py", 
        "core.py",
        "dialogs.py",
        "reviewer.py"
    ]
    
    deployed_files = []
    
    for filename in files_to_deploy:
        source_path = os.path.join(source_dir, filename)
        dest_path = filename
        
        if os.path.exists(source_path):
            try:
                shutil.copy2(source_path, dest_path)
                deployed_files.append(filename)
                print(f"✓ Deployed: {filename}")
            except Exception as e:
                print(f"✗ Failed to deploy {filename}: {e}")
                return False
        else:
            print(f"✗ Source file not found: {source_path}")
            return False
    
    print(f"\n✓ Successfully deployed {len(deployed_files)} files")
    return True

def verify_deployment():
    """Verify the deployment"""
    required_files = [
        "__init__.py",
        "const.py", 
        "core.py",
        "dialogs.py",
        "reviewer.py"
    ]
    
    all_present = True
    
    print("\nVerifying deployment:")
    for filename in required_files:
        if os.path.exists(filename):
            print(f"✓ {filename}")
        else:
            print(f"✗ {filename} missing")
            all_present = False
    
    return all_present

def main():
    """Main deployment function"""
    print("=" * 50)
    print("AnkiNexus Plugin Deployment")
    print("=" * 50)
    
    print("\n1. Backing up original files...")
    backup_file = backup_original()
    
    if backup_file:
        print("\n2. Deploying refactored files...")
        if deploy_refactored_files():
            print("\n3. Verifying deployment...")
            if verify_deployment():
                print("\n" + "=" * 50)
                print("🎉 DEPLOYMENT SUCCESSFUL!")
                print("\nWhat to do next:")
                print("1. Restart Anki completely")
                print("2. Test the plugin functionality:")
                print("   - Open a note in the editor")
                print("   - Look for the link button")
                print("   - Test linking cards")
                print("   - Test reviewing linked cards")
                print("3. If there are any issues:")
                print(f"   - Restore from backup: {backup_file}")
                print("   - Report the issue")
                print("\nFiles deployed:")
                print("- __init__.py (main entry point)")
                print("- const.py (constants)")
                print("- core.py (CardLinker class)")
                print("- dialogs.py (UI dialogs)")
                print("- reviewer.py (review functionality)")
                print("=" * 50)
            else:
                print("\n❌ DEPLOYMENT VERIFICATION FAILED!")
                print("Some files are missing. Please check the deployment.")
        else:
            print("\n❌ DEPLOYMENT FAILED!")
            print("Failed to deploy refactored files.")
    else:
        print("\n❌ BACKUP FAILED!")
        print("Cannot proceed without backing up original files.")

if __name__ == "__main__":
    main()
