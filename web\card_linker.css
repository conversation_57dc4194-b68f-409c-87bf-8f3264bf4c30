/* Card Linker Plugin Styles */

.card-linker-button {
    background-color: #4CAF50;
    border: none;
    color: white;
    padding: 8px 12px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    margin: 2px;
    cursor: pointer;
    border-radius: 4px;
}

.card-linker-button:hover {
    background-color: #45a049;
}

.linked-cards-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    background-color: #f9f9f9;
}

.linked-card-item {
    display: block;
    padding: 5px 10px;
    margin: 2px 0;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    text-decoration: none;
    color: #333;
}

.linked-card-item:hover {
    background-color: #f0f0f0;
    border-color: #4CAF50;
}

.linked-cards-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #555;
}
