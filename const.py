# -*- coding: utf-8 -*-
"""
Constants for Card Linker Plugin
"""

# PyQt6 compatibility fix
try:
    from PyQt6.QtCore import Qt
    from PyQt6.QtWidgets import QDialog
    USER_ROLE = Qt.ItemDataRole.UserRole
    DIALOG_ACCEPTED = QDialog.DialogCode.Accepted
except ImportError:
    try:
        from PyQt5.QtCore import Qt
        from PyQt5.QtWidgets import QDialog
        USER_ROLE = Qt.UserRole
        DIALOG_ACCEPTED = QDialog.Accepted
    except ImportError:
        USER_ROLE = 256
        DIALOG_ACCEPTED = 1

# Field names
LINKED_CARDS_FIELD = "LinkedCards"

# Default note type configuration
DEFAULT_NOTE_TYPE_CONFIG = {
    'name': 'Card Linker Note Type',
    'fields': ['Front', 'Back', LINKED_CARDS_FIELD],
    'template_name': 'Card 1',
    'front_template': '{{Front}}',
    'back_template': '{{FrontSide}}<hr id="answer">{{Back}}'
}
