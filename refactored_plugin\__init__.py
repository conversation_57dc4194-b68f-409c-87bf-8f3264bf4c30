# -*- coding: utf-8 -*-
"""
Card Linker Plugin for <PERSON><PERSON> linking related cards together to solve card fragmentation
"""

from aqt import gui_hooks
from .core import CardLinker
from .reviewer import (
    add_linked_cards_to_review,
    setup_link_handler
)

# 创建插件实例
card_linker = CardLinker()

# 注册编辑器按钮
def setup_editor_buttons(buttons, editor):
    return card_linker.setup_editor_button(buttons, editor)

gui_hooks.editor_did_init_buttons.append(setup_editor_buttons)

# 注册审阅器钩子
gui_hooks.reviewer_did_init.append(lambda x: setup_link_handler())
gui_hooks.card_will_show.append(add_linked_cards_to_review)