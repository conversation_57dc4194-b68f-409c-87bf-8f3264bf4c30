# refactor_config.py
# 定义我们的重构规则
TARGET_FILES = {
    'core.py': ['CardLinker'],
    'dialogs.py': ['LinkDialog', 'SimpleAddCardDialog'],
    'reviewer.py': [
        'add_linked_cards_to_review', 'check_card_reviewed_today',
        'is_card_in_current_deck', 'handle_linked_card_click',
        'show_card_preview', 'open_card_in_browser',
        'handle_suspended_card', 'get_current_time',
        'switch_to_target_card'
    ],
    'const.py': [], # 常量通常需要手动提取或用更复杂的逻辑
    '__init__.py': [] # 入口文件通常是手写的
}