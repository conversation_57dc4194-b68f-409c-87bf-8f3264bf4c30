#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validation script for the refactored plugin
This script performs comprehensive checks on the refactored code
"""

import os
import ast
import sys
from collections import defaultdict

def check_file_exists(filepath):
    """Check if file exists"""
    return os.path.exists(filepath)

def check_syntax(filepath):
    """Check Python syntax"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except Exception as e:
        return False, str(e)

def check_imports(filepath):
    """Check imports in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")
        
        return imports
    except Exception:
        return []

def check_class_definitions(filepath):
    """Check class definitions in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
        
        return classes
    except Exception:
        return []

def check_function_definitions(filepath):
    """Check function definitions in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
        
        return functions
    except Exception:
        return []

def main():
    """Main validation function"""
    print("=" * 60)
    print("AnkiNexus Plugin Refactoring Validation")
    print("=" * 60)
    
    plugin_dir = "refactored_plugin"
    files_to_check = {
        "__init__.py": "Main entry point",
        "const.py": "Constants and configuration", 
        "core.py": "Core CardLinker class",
        "dialogs.py": "Dialog classes",
        "reviewer.py": "Reviewer functionality"
    }
    
    all_passed = True
    
    # 1. File existence check
    print("\n1. File Existence Check:")
    print("-" * 30)
    for filename, description in files_to_check.items():
        filepath = os.path.join(plugin_dir, filename)
        exists = check_file_exists(filepath)
        status = "✓" if exists else "✗"
        print(f"{status} {filename}: {description}")
        if not exists:
            all_passed = False
    
    # 2. Syntax check
    print("\n2. Syntax Validation:")
    print("-" * 30)
    for filename in files_to_check.keys():
        filepath = os.path.join(plugin_dir, filename)
        if check_file_exists(filepath):
            valid, error = check_syntax(filepath)
            status = "✓" if valid else "✗"
            print(f"{status} {filename}: {'Valid syntax' if valid else f'Syntax error: {error}'}")
            if not valid:
                all_passed = False
    
    # 3. Import analysis
    print("\n3. Import Analysis:")
    print("-" * 30)
    for filename in files_to_check.keys():
        filepath = os.path.join(plugin_dir, filename)
        if check_file_exists(filepath):
            imports = check_imports(filepath)
            print(f"📦 {filename}:")
            for imp in imports[:5]:  # Show first 5 imports
                print(f"   - {imp}")
            if len(imports) > 5:
                print(f"   ... and {len(imports) - 5} more")
    
    # 4. Class and function analysis
    print("\n4. Code Structure Analysis:")
    print("-" * 30)
    
    structure = {}
    for filename in files_to_check.keys():
        filepath = os.path.join(plugin_dir, filename)
        if check_file_exists(filepath):
            classes = check_class_definitions(filepath)
            functions = check_function_definitions(filepath)
            structure[filename] = {"classes": classes, "functions": functions}
    
    for filename, content in structure.items():
        print(f"📁 {filename}:")
        if content["classes"]:
            print(f"   Classes: {', '.join(content['classes'])}")
        if content["functions"]:
            print(f"   Functions: {', '.join(content['functions'][:3])}{'...' if len(content['functions']) > 3 else ''}")
    
    # 5. Expected structure validation
    print("\n5. Expected Structure Validation:")
    print("-" * 30)
    
    expected_structure = {
        "core.py": {"classes": ["CardLinker"], "functions": []},
        "dialogs.py": {"classes": ["LinkDialog", "SimpleAddCardDialog"], "functions": []},
        "reviewer.py": {"classes": [], "functions": ["add_linked_cards_to_review"]},
        "const.py": {"classes": [], "functions": []},
        "__init__.py": {"classes": [], "functions": ["setup_editor_buttons"]}
    }
    
    for filename, expected in expected_structure.items():
        if filename in structure:
            actual = structure[filename]
            
            # Check classes
            for expected_class in expected["classes"]:
                if expected_class in actual["classes"]:
                    print(f"✓ {filename}: Class '{expected_class}' found")
                else:
                    print(f"✗ {filename}: Class '{expected_class}' missing")
                    all_passed = False
            
            # Check key functions
            for expected_func in expected["functions"]:
                if expected_func in actual["functions"]:
                    print(f"✓ {filename}: Function '{expected_func}' found")
                else:
                    print(f"✗ {filename}: Function '{expected_func}' missing")
                    all_passed = False
    
    # 6. Final summary
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 VALIDATION PASSED!")
        print("The refactoring appears to be successful.")
        print("\nNext steps:")
        print("1. Backup your original __init__.py file")
        print("2. Copy the refactored files to your plugin directory")
        print("3. Restart Anki and test the plugin functionality")
    else:
        print("❌ VALIDATION FAILED!")
        print("Please review and fix the issues mentioned above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
